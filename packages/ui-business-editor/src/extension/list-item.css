.youmind-editor-node-list-item-ui {
  display: list-item;
  margin: 4px 0;
}

/* 无序列表项样式 */
.youmind-editor-node-bullet-list-ui .youmind-editor-node-list-item-ui {
  list-style: none;
  position: relative;
  padding-left: 1.5em; /* 与有序列表保持一致的左边距 */

  &::before {
    content: "•";
    position: absolute;
    right: calc(100% - 1.5em + 0.2em); /* 使用与有序列表一致的定位方式 */
    top: 0;
    font-weight: normal;
    color: rgb(20, 86, 240); /* 与有序列表保持一致的蓝色 */
    font-size: inherit;
    line-height: inherit;
    text-align: right; /* 右对齐，与有序列表保持一致 */
    min-width: 1.2em; /* 与有序列表保持一致的最小宽度 */
  }

  /* 确保没有原生的列表标记 */
  &::marker {
    content: none;
  }
}

/* 有序列表项样式 - 使用简洁的循环模式 */
.youmind-editor-node-ordered-list-ui {
  counter-reset: list-counter;

  .youmind-editor-node-list-item-ui {
    list-style: none;
    position: relative;
    padding-left: 1.5em; /* 适中的左边距 */
    counter-increment: list-counter;

    /* 确保不会有原生的列表标记 */
    &::marker {
      content: none;
    }

    &::before {
      position: absolute;
      right: calc(100% - 1.5em + 0.2em); /* 适中的定位和间距 */
      top: 0;
      font-weight: normal;
      color: rgb(20, 86, 240);
      font-size: inherit;
      line-height: inherit;
      text-align: right; /* 右对齐，让标头向左展开 */
      min-width: 1.2em; /* 适中的最小宽度 */
    }
  }

  /* 嵌套列表重置计数器 */
  .youmind-editor-node-ordered-list-ui {
    counter-reset: list-counter;
  }
}

/*
 * 循环模式：数字 -> 字母 -> 罗马数字 -> 数字 -> ...
 * 使用简洁的选择器实现3层循环
 */

/* 层级 1, 4, 7, 10... - 数字 (1, 2, 3...) */
.youmind-editor-node-ordered-list-ui > .youmind-editor-node-list-item-ui::before,
.youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(list-counter, decimal) ". ";
}

/* 层级 2, 5, 8, 11... - 小写字母 (a, b, c...) */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before,
.youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(list-counter, lower-alpha) ". ";
}

/* 层级 3, 6, 9, 12... - 小写罗马数字 (i, ii, iii...) */
.youmind-editor-node-ordered-list-ui .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before,
.youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  .youmind-editor-node-ordered-list-ui
  > .youmind-editor-node-list-item-ui::before {
  content: counter(list-counter, lower-roman) ". ";
}
