import './bullet-list.css';
import { BulletList as BulletListBase } from '@repo/editor-common';

export const BulletList = BulletListBase.extend({
  addOptions() {
    return {
      itemTypeName: 'listItem',
      HTMLAttributes: {
        class: 'youmind-editor-node-bullet-list-ui',
      },
      keepMarks: false,
      keepAttributes: false,
    };
  },

  addCommands() {
    return {
      ...this.parent?.(),
      toggleBulletList: () => ({ commands, chain }) => {
        return chain()
          .toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)
          .command(({ tr, state }) => {
            // 设置新创建的 listItem 的 parentType 属性
            const { selection } = state;
            const { $from } = selection;

            // 查找当前选区内的所有 listItem 节点
            tr.doc.nodesBetween(selection.from, selection.to, (node, pos) => {
              if (node.type.name === this.options.itemTypeName) {
                // 检查这个 listItem 是否在 bulletList 中
                const resolvedPos = tr.doc.resolve(pos);
                for (let i = resolvedPos.depth; i > 0; i--) {
                  const ancestor = resolvedPos.node(i);
                  if (ancestor.type.name === this.name) {
                    // 设置 parentType 属性
                    tr.setNodeMarkup(pos, undefined, {
                      ...node.attrs,
                      parentType: 'bulletList',
                    });
                    break;
                  }
                }
              }
            });

            return true;
          })
          .run();
      },
    };
  },
});
