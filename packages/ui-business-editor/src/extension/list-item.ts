import './list-item.css';
import { ListItem as ListItemBase } from '@repo/editor-common';
import { mergeAttributes } from '@tiptap/core';

export const ListItem = ListItemBase.extend({
  addOptions() {
    return {
      HTMLAttributes: {
        class: 'youmind-editor-node-list-item-ui',
      },
      bulletListTypeName: 'bulletList',
      orderedListTypeName: 'orderedList',
    };
  },

  addAttributes() {
    return {
      ...this.parent?.(),
      listType: {
        default: null,
        parseHTML: (element) => {
          // 从父级元素推断列表类型
          const parentList = element.closest('ul, ol');
          if (parentList?.tagName === 'UL') return 'bullet';
          if (parentList?.tagName === 'OL') return 'ordered';
          return null;
        },
        renderHTML: (attributes) => {
          return attributes.listType ? { 'data-list-type': attributes.listType } : {};
        },
      },
    };
  },

  renderHTML({ HTMLAttributes, node }) {
    // 根据 listType 属性添加上下文感知的类名
    const listType = node.attrs.listType;
    let contextClass = '';

    if (listType === 'bullet') {
      contextClass = 'youmind-editor-list-item-in-bullet';
    } else if (listType === 'ordered') {
      contextClass = 'youmind-editor-list-item-in-ordered';
    }

    return [
      'li',
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
        class: `${this.options.HTMLAttributes.class} ${contextClass}`.trim(),
        'data-list-type': listType,
      }),
      0,
    ];
  },
});
